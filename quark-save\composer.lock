{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "42eb5285e47ec6fe7fada0508e5a4977", "packages": [{"name": "openspout/openspout", "version": "v4.30.0", "source": {"type": "git", "url": "https://github.com/openspout/openspout.git", "reference": "df9b0f4d229c37c3caa5a9252a6ad8a94efb0fb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/openspout/openspout/zipball/df9b0f4d229c37c3caa5a9252a6ad8a94efb0fb5", "reference": "df9b0f4d229c37c3caa5a9252a6ad8a94efb0fb5", "shasum": ""}, "require": {"ext-dom": "*", "ext-fileinfo": "*", "ext-filter": "*", "ext-libxml": "*", "ext-xmlreader": "*", "ext-zip": "*", "php": "~8.3.0 || ~8.4.0"}, "require-dev": {"ext-zlib": "*", "friendsofphp/php-cs-fixer": "^3.75.0", "infection/infection": "^0.29.14", "phpbench/phpbench": "^1.4.1", "phpstan/phpstan": "^2.1.16", "phpstan/phpstan-phpunit": "^2.0.6", "phpstan/phpstan-strict-rules": "^2.0.4", "phpunit/phpunit": "^12.1.5"}, "suggest": {"ext-iconv": "To handle non UTF-8 CSV files (if \"php-mbstring\" is not already installed or is too limited)", "ext-mbstring": "To handle non UTF-8 CSV files (if \"iconv\" is not already installed)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}, "autoload": {"psr-4": {"OpenSpout\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP Library to read and write spreadsheet files (CSV, XLSX and ODS), in a fast and scalable way", "homepage": "https://github.com/openspout/openspout", "keywords": ["OOXML", "csv", "excel", "memory", "odf", "ods", "office", "open", "php", "read", "scale", "spreadsheet", "stream", "write", "xlsx"], "support": {"issues": "https://github.com/openspout/openspout/issues", "source": "https://github.com/openspout/openspout/tree/v4.30.0"}, "funding": [{"url": "https://paypal.me/filippotessarotto", "type": "custom"}, {"url": "https://github.com/Slamdunk", "type": "github"}], "time": "2025-05-20T12:33:06+00:00"}, {"name": "pear/console_table", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/pear/Console_Table.git", "reference": "1930c11897ca61fd24b95f2f785e99e0f36dcdea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Console_Table/zipball/1930c11897ca61fd24b95f2f785e99e0f36dcdea", "reference": "1930c11897ca61fd24b95f2f785e99e0f36dcdea", "shasum": ""}, "require": {"php": ">=5.2.0"}, "suggest": {"pear/Console_Color2": ">=0.1.2"}, "type": "library", "autoload": {"classmap": ["Table.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://pear.php.net/user/yunosh"}, {"name": "<PERSON><PERSON>", "homepage": "http://pear.php.net/user/tal"}, {"name": "<PERSON>", "homepage": "http://pear.php.net/user/xnoguer"}, {"name": "<PERSON>", "homepage": "http://pear.php.net/user/richard"}], "description": "Library that makes it easy to build console style tables.", "homepage": "http://pear.php.net/package/Console_Table/", "keywords": ["console"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Console_Table", "source": "https://github.com/pear/Console_Table"}, "time": "2018-01-25T20:47:17+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.4", "ext-curl": "*", "ext-json": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}