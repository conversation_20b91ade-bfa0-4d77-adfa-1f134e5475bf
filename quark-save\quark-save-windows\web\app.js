// 主应用JavaScript
class QuarkApp {
    constructor() {
        this.logContainer = document.getElementById('log-container');
        this.statusText = document.getElementById('status-text');
        this.statusBadge = document.getElementById('status-badge');
        this.init();
    }

    async init() {
        await this.checkStatus();
        this.setupEventListeners();
        this.startStatusPolling();
    }

    async checkStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            if (data.status === 'ok') {
                if (data.hasCookie) {
                    this.updateStatus('系统就绪，Cookie已配置', 'success', '就绪');
                } else {
                    this.updateStatus('请先配置Cookie', 'warning', '需要配置');
                }
            } else {
                this.updateStatus('系统错误', 'error', '错误');
            }
        } catch (error) {
            this.updateStatus('连接失败', 'error', '离线');
            console.error('Status check failed:', error);
        }
    }

    updateStatus(text, type, badge) {
        this.statusText.textContent = text;
        this.statusBadge.textContent = badge;
        this.statusBadge.className = `badge bg-${this.getBootstrapColor(type)}`;
    }

    getBootstrapColor(type) {
        const colors = {
            'success': 'success',
            'error': 'danger',
            'warning': 'warning',
            'info': 'info'
        };
        return colors[type] || 'secondary';
    }

    setupEventListeners() {
        // 设置模态框事件
        const saveModal = new bootstrap.Modal(document.getElementById('saveModal'));
        const searchModal = new bootstrap.Modal(document.getElementById('searchModal'));
        
        window.showSaveDialog = () => saveModal.show();
        window.showSearchDialog = () => searchModal.show();
        
        // 设置全局函数
        window.executeAction = this.executeAction.bind(this);
        window.executeSave = this.executeSave.bind(this);
        window.executeSearch = this.executeSearch.bind(this);
    }

    async executeAction(action, params = {}) {
        this.addLog(`开始执行: ${this.getActionName(action)}`, 'info');
        this.showLoading(true);

        try {
            const response = await fetch('/api/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: action,
                    params: params
                })
            });

            const data = await response.json();
            
            if (data.status === 'success') {
                this.addLog(`执行成功: ${this.getActionName(action)}`, 'success');
                this.addLog(data.output, 'info');
            } else {
                this.addLog(`执行失败: ${this.getActionName(action)}`, 'error');
                this.addLog(data.output, 'error');
            }
        } catch (error) {
            this.addLog(`网络错误: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async executeSave() {
        const fileInput = document.getElementById('fileInput');
        const filePathInput = document.getElementById('filePathInput');
        
        let filePath = filePathInput.value.trim();
        
        if (fileInput.files.length > 0) {
            // 如果选择了文件，使用文件名
            filePath = fileInput.files[0].name;
            // 这里可以添加文件上传逻辑
            this.addLog('注意: 文件上传功能需要将文件放在程序目录下', 'warning');
        }
        
        if (!filePath) {
            this.addLog('请选择文件或输入文件路径', 'error');
            return;
        }

        bootstrap.Modal.getInstance(document.getElementById('saveModal')).hide();
        await this.executeAction('save', { path: filePath });
    }

    async executeSearch() {
        const dramaNameInput = document.getElementById('dramaNameInput');
        const dramaName = dramaNameInput.value.trim();
        
        if (!dramaName) {
            this.addLog('请输入短剧名称', 'error');
            return;
        }

        bootstrap.Modal.getInstance(document.getElementById('searchModal')).hide();
        await this.executeAction('auto', { name: dramaName });
    }

    addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;
        logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
        
        this.logContainer.appendChild(logEntry);
        this.logContainer.scrollTop = this.logContainer.scrollHeight;
        
        // 限制日志条目数量
        const entries = this.logContainer.querySelectorAll('.log-entry');
        if (entries.length > 100) {
            entries[0].remove();
        }
    }

    showLoading(show) {
        const buttons = document.querySelectorAll('button[onclick^="executeAction"]');
        buttons.forEach(button => {
            if (show) {
                button.disabled = true;
                const originalText = button.innerHTML;
                button.dataset.originalText = originalText;
                button.innerHTML = '<span class="loading"></span> 执行中...';
            } else {
                button.disabled = false;
                if (button.dataset.originalText) {
                    button.innerHTML = button.dataset.originalText;
                }
            }
        });
    }

    getActionName(action) {
        const names = {
            'sign': '自动签到',
            'save': '转存资源',
            'share': '分享资源',
            'syn_dir': '同步目录',
            'auto': '自动更新'
        };
        return names[action] || action;
    }

    startStatusPolling() {
        // 每30秒检查一次状态
        setInterval(() => {
            this.checkStatus();
        }, 30000);
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new QuarkApp();
});

// 通知权限请求
function requestNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
}

// 显示桌面通知
function showNotification(title, message, type = 'info') {
    if ('Notification' in window && Notification.permission === 'granted') {
        const notification = new Notification(title, {
            body: message,
            icon: '/favicon.ico',
            tag: 'quark-app'
        });

        setTimeout(() => notification.close(), 5000);
    }
}
