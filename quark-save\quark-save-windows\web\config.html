<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 夸克网盘自动化工具</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>☁️</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 顶部导航 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="bi bi-cloud-arrow-down"></i>
                    夸克网盘自动化工具
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">
                        <i class="bi bi-house"></i> 首页
                    </a>
                </div>
            </div>
        </nav>

        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <!-- Cookie配置 -->
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="bi bi-key"></i> Cookie 配置</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle"></i> 如何获取Cookie：</h6>
                                <ol class="mb-0">
                                    <li>打开浏览器，访问 <a href="https://pan.quark.cn" target="_blank">https://pan.quark.cn</a> 并登录</li>
                                    <li>按 F12 打开开发者工具</li>
                                    <li>切换到 "网络"(Network) 标签</li>
                                    <li>刷新页面</li>
                                    <li>在请求列表中找到任意一个请求，查看请求头中的 Cookie</li>
                                    <li>复制完整的 Cookie 内容并粘贴到下方文本框</li>
                                </ol>
                            </div>
                            
                            <form id="cookieForm">
                                <div class="mb-3">
                                    <label for="cookieInput" class="form-label">Cookie 内容</label>
                                    <textarea class="form-control" id="cookieInput" rows="4" 
                                              placeholder="请粘贴从浏览器开发者工具中获取的完整Cookie内容..."></textarea>
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="button" class="btn btn-outline-secondary" onclick="loadCurrentCookie()">
                                        <i class="bi bi-arrow-clockwise"></i> 加载当前配置
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="saveCookie()">
                                        <i class="bi bi-check-lg"></i> 保存配置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 系统信息 -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="bi bi-info-square"></i> 系统信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>PHP 版本:</strong> <span id="phpVersion">-</span></p>
                                    <p><strong>Cookie 状态:</strong> <span id="cookieStatus" class="badge bg-secondary">检查中</span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>服务状态:</strong> <span id="serviceStatus" class="badge bg-success">运行中</span></p>
                                    <p><strong>最后更新:</strong> <span id="lastUpdate">-</span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 高级设置 -->
                    <div class="card mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0"><i class="bi bi-sliders"></i> 高级设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="requestDelay" class="form-label">请求间隔 (秒)</label>
                                        <input type="number" class="form-control" id="requestDelay" value="2" min="1" max="10">
                                        <div class="form-text">设置API请求之间的间隔时间，避免被限制</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maxRetries" class="form-label">最大重试次数</label>
                                        <input type="number" class="form-control" id="maxRetries" value="3" min="1" max="10">
                                        <div class="form-text">操作失败时的最大重试次数</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                                <label class="form-check-label" for="autoRefresh">
                                    自动刷新日志
                                </label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="enableNotifications">
                                <label class="form-check-label" for="enableNotifications">
                                    启用桌面通知
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- API设置 -->
                    <div class="card mb-4">
                        <div class="card-header bg-gradient" style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4);">
                            <h5 class="mb-0 text-white"><i class="bi bi-cloud-download"></i> API设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="apiUrl" class="form-label">API地址</label>
                                        <input type="text" class="form-control" id="apiUrl" value="https://www.duanju.click/api/short/quark" readonly>
                                        <div class="form-text">短剧API接口地址</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="apiTimeout" class="form-label">API超时时间 (秒)</label>
                                        <input type="number" class="form-control" id="apiTimeout" value="30" min="5" max="120">
                                        <div class="form-text">API请求超时时间</div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="apiAutoSaveEnabled" checked>
                                <label class="form-check-label" for="apiAutoSaveEnabled">
                                    API获取后自动转存
                                </label>
                            </div>

                            <div class="d-grid gap-2 d-md-flex">
                                <button type="button" class="btn btn-outline-primary" onclick="testApiConnection()">
                                    <i class="bi bi-wifi"></i> 测试API连接
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="createScheduledTask()">
                                    <i class="bi bi-clock"></i> 创建定时任务
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="bi bi-tools"></i> 系统操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2 d-md-flex">
                                <button type="button" class="btn btn-outline-warning" onclick="clearLogs()">
                                    <i class="bi bi-trash"></i> 清空日志
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="exportConfig()">
                                    <i class="bi bi-download"></i> 导出配置
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="importConfig()">
                                    <i class="bi bi-upload"></i> 导入配置
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="resetConfig()">
                                    <i class="bi bi-arrow-counterclockwise"></i> 重置配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="messageToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-info-circle text-primary me-2"></i>
                <strong class="me-auto">系统消息</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                消息内容
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="config.js"></script>
</body>
</html>
