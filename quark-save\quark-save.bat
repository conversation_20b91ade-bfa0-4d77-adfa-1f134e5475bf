@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 设置脚本目录
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:: 设置PHP路径
set "PHP_PATH=%SCRIPT_DIR%php\php.exe"

:: 检查PHP是否存在
if not exist "%PHP_PATH%" (
    echo 错误：找不到PHP运行时！
    echo 请确保php目录存在且包含php.exe文件。
    pause
    exit /b 1
)

:: 显示菜单
:menu
cls
echo ========================================
echo           夸克网盘自动化工具
echo ========================================
echo.
echo 请选择要执行的操作：
echo.
echo 1. 自动签到
echo 2. 转存资源 (需要指定文件路径)
echo 3. 分享资源
echo 4. 同步目录
echo 5. 自动更新全部短剧
echo 6. 自动更新每日新增短剧
echo 7. 搜索并转存短剧
echo 8. 退出
echo.
set /p choice=请输入选项 (1-8): 

if "%choice%"=="1" goto sign
if "%choice%"=="2" goto save
if "%choice%"=="3" goto share
if "%choice%"=="4" goto syn_dir
if "%choice%"=="5" goto auto_all
if "%choice%"=="6" goto auto_daily
if "%choice%"=="7" goto search
if "%choice%"=="8" goto exit
echo 无效选项，请重新选择！
pause
goto menu

:sign
echo.
echo 正在执行自动签到...
"%PHP_PATH%" QuarkService.php --options sign
pause
goto menu

:save
echo.
set /p file_path=请输入转存文件路径 (支持txt、csv、xlsx、xls格式): 
if "%file_path%"=="" (
    echo 文件路径不能为空！
    pause
    goto menu
)
echo 正在转存资源...
"%PHP_PATH%" QuarkService.php --options save --path "%file_path%"
pause
goto menu

:share
echo.
echo 正在分享资源...
"%PHP_PATH%" QuarkService.php --options share
pause
goto menu

:syn_dir
echo.
echo 正在同步目录...
"%PHP_PATH%" QuarkService.php --options syn_dir
pause
goto menu

:auto_all
echo.
echo 正在自动更新全部短剧...
"%PHP_PATH%" QuarkService.php --options auto --update all
pause
goto menu

:auto_daily
echo.
echo 正在自动更新每日新增短剧...
"%PHP_PATH%" QuarkService.php --options auto --update daily
pause
goto menu

:search
echo.
set /p drama_name=请输入要搜索的短剧名称: 
if "%drama_name%"=="" (
    echo 短剧名称不能为空！
    pause
    goto menu
)
echo 正在搜索并转存短剧...
"%PHP_PATH%" QuarkService.php --options auto --name "%drama_name%"
pause
goto menu

:exit
echo 感谢使用夸克网盘自动化工具！
exit /b 0
